import {
  unstable_composeUploadHandlers,
  unstable_createMemoryUploadHand<PERSON>,
  unstable_parseMultipartFormData,
} from "@remix-run/node";

import { CreateNoteFormStruct } from "~/routes/_auth.notes.create.($id)/types";
import { formDataToObject } from "~/utils/validation";

// NOTE: @deboj<PERSON><PERSON>ghosh Since the following exports rely on server side methods, they should be moved to a "server utils" folder.

export const getPayloadFromForm = async (request: Request) => {
  // Parse formData using <PERSON>'s multipart form handlers
  // See https://remix.run/docs/en/main/utils/parse-multipart-form-data
  const formData = await unstable_parseMultipartFormData(
    request,
    unstable_composeUploadHandlers(
      unstable_createMemoryUploadHandler({
        maxPartSize: 500_000_000, // 500MB
      })
    )
  );

  return CreateNoteFormStruct.parse(formDataToObject(formData));
};
