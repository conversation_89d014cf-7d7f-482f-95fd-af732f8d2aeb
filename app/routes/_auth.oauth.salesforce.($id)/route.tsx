import { LoaderFunctionArgs, redirect } from "@remix-run/node";
import { setUpOAuthProvider } from "~/api/oauth/setUpOAuthProvider.server";
import { OAuthRequestProviderEnum } from "~/api/openapi/generated";
import { logError } from "~/utils/log.server";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const authorizationCode = new URL(request.url).searchParams.get("code");
  if (!authorizationCode) {
    throw new Error("Missing authorization_code query parameter");
  }

  const { id = "salesforce" } = params;

  let integrationName = "";
  switch (id) {
    case "salesforce":
      integrationName = "Salesforce";
      break;
    case "salentica-elements":
      integrationName = "Salentica Elements";
      break;
    case "black-diamond":
      integrationName = "Black Diamond";
      break;
    case "xlr8":
      integrationName = "XLR8";
      break;
    default:
      // eslint-disable-next-line no-console
      console.error(`Could not find name for Salesforce integration: ${id}`);
  }

  try {
    await setUpOAuthProvider({
      authorizationCode,
      provider: OAuthRequestProviderEnum.Salesforce,
      request,
    });
    return redirect(
      `/settings/integrations?type=integration&status=true&name=${integrationName}`
    );
  } catch (error) {
    logError("Failed to setup Salesforce integration", error);
    return redirect(
      `/settings/integrations?type=integration&status=false&name=${integrationName}`
    );
  }
};
