import ScheduledCard from "./components/ScheduledCard";
import {
  defer,
  MetaFunction,
  LoaderFunctionArgs,
  ActionFunction,
} from "@remix-run/node";
import {
  Await,
  useFetcher,
  useLoaderData,
  useNavigate,
  useLocation,
  NavLink,
  json,
} from "@remix-run/react";
import { addHours, isAfter, isSameDay, subDays } from "date-fns";
import { Suspense, useEffect, useState } from "react";
import { toast } from "react-toastify";
import { Steps } from "intro.js-react";

import { ConfirmModal } from "~/@ui/ConfirmModal";
import { ContentV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { getClients } from "~/api/notes/getClients.server";
import { getNotes } from "~/api/notes/getNotes.server";
import { uploadNoteToCrm } from "~/api/notes/uploadToCrm.server";
import { configurationParameters } from "~/api/openapi/configParams";
import {
  Configuration,
  CalendarApi,
  CalendarEvent,
  ListNotesResponse,
  NoteApi,
  MeetingArtifactsApi,
  ProcessingStatus,
  ClientInteraction,
  ScheduledEvent,
} from "~/api/openapi/generated";
import { getClientTimeZone } from "~/utils/hints";
import { logError } from "~/utils/log.server";
import { combineEvents, isNoteEmpty } from "~/utils/notesUtils";
import PastNoteCard from "./components/PastNoteCard";
import { Typography } from "~/@ui/Typography";
import { Fab } from "~/@ui/Fab";
import { AddOutlined } from "@mui/icons-material";
import { Skeleton } from "~/@shadcn/ui/skeleton";
import { Divider } from "~/@ui/Divider";
import { useTailwindBreakpoints } from "~/utils/useTailwindBreakpoints";
import OnboardingScheduledCard from "./components/OnboardingScheduledCard";
import { tutorialSteps } from "./utils";
import useOnboarding from "~/utils/useOnboarding";
import MeetingTabs from "./components/Tabs";
import { isFlagEnabled } from "~/utils/flagsInCookies";
import { SerializeFrom } from "~/types/remix";

type FetcherData = { success?: boolean; error?: string };

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const now = Math.floor(Date.now() / 1000);
  const thirtyDaysAgo = now - 30 * 24 * 60 * 60;
  const sevenDaysFromNow = now + 7 * 24 * 60 * 60;

  const clientPromise = getClients({ searchTerm: "", request }).catch(() => ({
    crmSystem: undefined,
  }));

  const notesPromise = getNotes({
    request,
    notBefore: thirtyDaysAgo,
    notAfter: sevenDaysFromNow,
  }).catch(() => [] as ListNotesResponse[]);

  const configuration = await configurationParameters(request);
  const interactionsPromise = new MeetingArtifactsApi(
    new Configuration(configuration)
  )
    .meetingArtifactsListClientInteractions()
    .catch(() => [] as ClientInteraction[]);

  const config = new Configuration(await configurationParameters(request));

  let calendarEventsPromise: Promise<
    ScheduledEvent[] | CalendarEvent[] | undefined
  > = isFlagEnabled(request, "UseScheduledEventsForCalendarAPI")
    ? new CalendarApi(config)
        .calendarScheduledCalendarEvents(
          { timeZone: getClientTimeZone(request) },
          { signal: AbortSignal.timeout(5000) }
        )
        .catch(() => undefined)
    : new CalendarApi(config)
        .calendarListEvents(
          { timeZone: getClientTimeZone(request) },
          { signal: AbortSignal.timeout(5000) }
        )
        .catch(() => undefined);

  const [{ crmSystem }, notesData, clientInteractionsData] = await Promise.all([
    clientPromise,
    notesPromise,
    interactionsPromise,
  ]);

  return defer({
    crmSystem,
    notes: notesData,
    clientInteractions: clientInteractionsData,
    calendarEventsPromise,
  });
};

export const action: ActionFunction = async ({ request }) => {
  try {
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("multipart/form-data")) {
      throw new Error("Unsupported content type");
    }

    const formData = await request.formData();
    const actionType = formData.get("actionType");
    const noteId = formData.get("noteId");

    if (typeof actionType !== "string") {
      return json(
        {
          errors: ["Invalid form data: actionType must be strings"],
        },
        { status: 400 }
      );
    }

    const configuration = new Configuration(
      await configurationParameters(request)
    );

    switch (actionType) {
      case "send-to-crm": {
        if (typeof noteId !== "string") {
          return json(
            { errors: ["Invalid form data: noteId must be a string"] },
            { status: 400 }
          );
        }
        const uploadTargetID = formData.get("uploadTargetID") as string | null;
        return await uploadNoteToCrm({
          noteId,
          request,
          uploadTargetID: uploadTargetID ?? undefined,
        });
      }
      case "delete-note": {
        if (typeof noteId !== "string") {
          return json(
            { errors: ["Invalid form data: noteId must be a string"] },
            { status: 400 }
          );
        }
        await new NoteApi(configuration).noteDeleteNote({ noteId });
        return json({ success: true });
      }
      case "update-auto-join": {
        const autojoinEnabled = formData.get("autojoinEnabled");
        const scheduledEventUUID = formData.get("scheduledEventUUID");
        if (typeof scheduledEventUUID !== "string") {
          return json(
            {
              errors: [
                "Invalid form data: scheduledEventUUID must be a string",
              ],
            },
            { status: 400 }
          );
        }

        await new CalendarApi(configuration).calendarUpdateAutojoin({
          bodyCalendarUpdateAutojoin: {
            autoJoin: autojoinEnabled === "true",
            scheduledEventUuid: scheduledEventUUID as string,
          },
        });
        return json({ success: true });
      }

      default:
        return json(
          { errors: [`Unknown action type: ${actionType}`] },
          { status: 400 }
        );
    }
  } catch (error) {
    logError("app/routes/hub.tsx action", error);
    return json(
      { errors: ["Failed to update meeting due to server error"] },
      { status: 500 }
    );
  }
};

export const meta: MetaFunction = () => [
  { title: "Advisor Hub" },
  { name: "description", content: "View your hub" },
];

const AdvisorHub = () => {
  const { notes, clientInteractions, calendarEventsPromise, crmSystem } =
    useLoaderData<typeof loader>();
  const fetcher = useFetcher<FetcherData>();
  const navigate = useNavigate();
  const location = useLocation();
  const tabFromSearchParams =
    location.search && new URLSearchParams(location.search).get("tab");
  const [currentTab, setCurrentTab] = useState(
    tabFromSearchParams && tabFromSearchParams.length > 0
      ? tabFromSearchParams
      : "today"
  );
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentNote, setCurrentNote] = useState("");
  const breakpoints = useTailwindBreakpoints();
  const useDesktopLayout = breakpoints.matchedBreakpoints.has("sm");
  const { isTutorialEnabled, completeTutorial } = useOnboarding("dashboard", {
    triggerViaUrl: true,
  });

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.success) {
        toast.update("sync-to-crm", {
          render: "Note synced to CRM",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
        toast.update("delete-note", {
          render: "Note deleted",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
      } else {
        toast.update("sync-to-crm", {
          render: fetcher.data.error || "Failed to sync to CRM",
          type: toast.TYPE.ERROR,
          isLoading: false,
          autoClose: 2000,
        });
        toast.update("delete-note", {
          render: "Failed to delete note",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
      }
    }
  }, [fetcher.state, fetcher.data]);

  const openDeleteModal = (uuid: string) => {
    setIsDeleteModalOpen(true);
    setCurrentNote(uuid);
  };

  const closeDeleteModal = () => setIsDeleteModalOpen(false);

  const handleDelete = () => {
    const formData = new FormData();
    formData.append("actionType", "delete-note");
    formData.append("noteId", currentNote);
    fetcher.submit(formData, {
      method: "post",
      encType: "multipart/form-data",
    });
    toast.loading("Deleting note", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      progress: undefined,
      toastId: "delete-note",
    });
  };

  const handleSync = (uuid: string) => {
    const formData = new FormData();
    formData.append("actionType", "send-to-crm");
    formData.append("noteId", uuid);
    fetcher.submit(formData, {
      method: "post",
      encType: "multipart/form-data",
    });

    toast.loading("Sending to CRM", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      toastId: "sync-to-crm",
    });
  };

  const confirmDelete = () => {
    handleDelete();
    closeDeleteModal();
  };

  const groupNotesByTime = (notes: ListNotesResponse[]) => {
    const finishedToday: ListNotesResponse[] = [];
    const recent: ListNotesResponse[] = [];
    const upcoming: ListNotesResponse[] = [];

    const now = new Date();
    const oneMonthAgo = subDays(now, 30);

    notes.forEach((note) => {
      // TODO: always use the note's scheduled end time
      const referenceDate =
        note.status == ProcessingStatus.Scheduled
          ? note.scheduledEndTime
            ? new Date(note.scheduledEndTime)
            : addHours(new Date(note.scheduledStartTime ?? note.created), 1)
          : new Date(note.created);
      if (referenceDate > now) {
        upcoming.push(note);
      } else if (isSameDay(referenceDate, now)) {
        finishedToday.push(note);
      } else if (isAfter(referenceDate, oneMonthAgo)) {
        recent.push(note);
      }
    });

    return { upcoming, finishedToday, recent };
  };

  const { upcoming, finishedToday, recent } = groupNotesByTime(notes);

  const renderNotesSection = (
    calendarEvents: SerializeFrom<ScheduledEvent | CalendarEvent>[],
    upcomingNotes: ListNotesResponse[],
    todaysFinishedNotes: ListNotesResponse[],
    pastNotes: ListNotesResponse[],
    showLoading: boolean
  ) => {
    // We need to handle all meeting source IDs; if a calendar event moved, we don't want it to show
    // up again in the upcoming section if there is an associated note.
    const meetingSourceIDs = new Set(
      upcomingNotes
        .concat(pastNotes)
        .concat(todaysFinishedNotes)
        .map((note) => note.meetingSourceId)
        .filter((id): id is string => id !== null)
    );

    const currentTabIsToday = currentTab === "today";

    const combinedEvents = combineEvents(
      calendarEvents,
      upcomingNotes,
      clientInteractions,
      meetingSourceIDs,
      currentTabIsToday
    );

    // in case of onboarding flow, render the mock card
    if (isTutorialEnabled) {
      return <OnboardingScheduledCard useDesktopLayout={useDesktopLayout} />;
    }

    if (currentTab === "past") {
      return (
        <>
          {pastNotes
            .filter((note) => note.status !== ProcessingStatus.Unknown)
            .map((note) => (
              <PastNoteCard
                key={note.uuid}
                noteId={note.uuid}
                client={note.client}
                meetingName={note.meetingName}
                meetingType={note.meetingType}
                date={new Date(note.created)}
                status={note.status}
                isEmpty={isNoteEmpty(note)}
                crmSystem={crmSystem}
                to={{ pathname: `/notes/${note.uuid}` }}
                syncToCRM={() => handleSync(note.uuid)}
                onDelete={() => openDeleteModal(note.uuid)}
                useDesktopLayout={useDesktopLayout}
              />
            ))}
        </>
      );
    }

    return (
      <>
        {combinedEvents.length > 0 ? (
          combinedEvents.map((event) => (
            <ScheduledCard
              key={event.id}
              toPrep={event.toPrep}
              toMeeting={event.toMeeting}
              meetingName={event.meetingName}
              scheduledStartTime={new Date(event.startTime)}
              scheduledEndTime={
                event.endTime ? new Date(event.endTime) : undefined
              }
              interaction={event.interaction}
              attendees={event.attendees ?? []}
              meetingType={event.meetingType || undefined}
              active={
                isSameDay(new Date(event.startTime), new Date()) &&
                new Date() >= new Date(event.startTime) &&
                (!event.endTime || new Date() <= new Date(event.endTime))
              }
              status={event.status || ProcessingStatus.Scheduled}
              meetingLink={event.meetingLink}
              useDesktopLayout={useDesktopLayout}
              autojoinAvailable={event.autojoinAvailable}
              autojoinEnabled={event.autojoinEnabled}
              scheduledEventUUID={event.scheduledEventUUID}
            />
          ))
        ) : showLoading ? (
          <>
            <Skeleton className="h-36 w-full rounded-md" />
            <Skeleton className="h-36 w-full rounded-md" />
            {!currentTabIsToday && (
              <>
                <Skeleton className="h-36 w-full rounded-md" />
                <Skeleton className="h-36 w-full rounded-md" />
                <Skeleton className="h-36 w-full rounded-md" />
                <Skeleton className="h-36 w-full rounded-md" />
              </>
            )}
          </>
        ) : (
          <Typography variant="h3">
            🎉 No more meetings{currentTabIsToday ? " today" : ""}!
          </Typography>
        )}
        {currentTab === "today" && todaysFinishedNotes.length > 0 && (
          <Divider />
        )}
        {currentTab === "today" &&
          todaysFinishedNotes.map((note) => (
            <PastNoteCard
              key={note.uuid}
              noteId={note.uuid}
              client={note.client}
              meetingName={note.meetingName}
              meetingType={note.meetingType}
              date={new Date(note.created)}
              status={note.status}
              isEmpty={isNoteEmpty(note)}
              crmSystem={crmSystem}
              to={{ pathname: `/notes/${note.uuid}` }}
              syncToCRM={() => handleSync(note.uuid)}
              onDelete={() => openDeleteModal(note.uuid)}
              useDesktopLayout={useDesktopLayout}
            />
          ))}
      </>
    );
  };

  const onTabChange = (newValue: string) => {
    setCurrentTab(newValue);
    navigate({ search: `tab=${newValue}` }, { replace: true });
  };

  const onCompleteTutorial = () => {
    completeTutorial();
    navigate("/dashboard", { replace: true });
  };

  const onExitTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      onCompleteTutorial();
    }
  };

  return (
    <LayoutV2>
      <ContentV2
        floatingAction={
          <Fab asChild>
            <NavLink to="/notes/create">
              <AddOutlined />
            </NavLink>
          </Fab>
        }
        className="w-80 min-w-80"
      >
        <Steps
          enabled={isTutorialEnabled}
          steps={tutorialSteps}
          initialStep={0}
          onExit={onExitTutorial}
          onComplete={onCompleteTutorial}
          options={{
            exitOnOverlayClick: false,
          }}
        />

        <div className="mb-4 flex w-full flex-col items-center gap-4 sm:mb-0 sm:p-4">
          <MeetingTabs
            onChange={onTabChange}
            value={currentTab}
            useDesktopLayout={useDesktopLayout}
          />
          <Suspense
            fallback={renderNotesSection(
              [],
              upcoming,
              finishedToday,
              [...finishedToday, ...recent],
              true
            )}
          >
            <Await
              resolve={calendarEventsPromise}
              errorElement={
                <>
                  <Typography color="error">
                    Error fetching calendar events.
                  </Typography>
                  {renderNotesSection(
                    [],
                    upcoming,
                    finishedToday,
                    [...finishedToday, ...recent],
                    false
                  )}
                </>
              }
            >
              {(calendarEvents) => {
                if (!calendarEvents) {
                  return (
                    <>
                      <Typography color="error">
                        Error fetching calendar events.
                      </Typography>
                      {renderNotesSection(
                        [],
                        upcoming,
                        finishedToday,
                        [...finishedToday, ...recent],
                        false
                      )}
                    </>
                  );
                }

                return renderNotesSection(
                  calendarEvents.filter((e) => !!e),
                  upcoming,
                  finishedToday,
                  [...finishedToday, ...recent],
                  false
                );
              }}
            </Await>
          </Suspense>
        </div>
        <ConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={closeDeleteModal}
          onConfirm={confirmDelete}
          title="Confirm Delete"
          message="Are you sure you want to delete this note? This will delete the note for all users it's shared with."
        />
      </ContentV2>
    </LayoutV2>
  );
};

export default AdvisorHub;
